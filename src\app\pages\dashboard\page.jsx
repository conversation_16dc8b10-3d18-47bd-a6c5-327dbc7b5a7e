"use client";
import { useRef, useEffect, useState } from "react";
import Image from "next/image";
import styles from "./dashboard.module.css";
import Graph from "../../components/Dashboard/Graph/page";
import Layout from "../../components/Layout/page";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useTimer } from "@/app/context/TimerContext";
import { getToken } from "@/app/context/SSEContext";

const Dashboard = () => {
  const router = useRouter();
  const [transactionData, setTransactionData] = useState([]);
  const [dashboardData, setDashboardData] = useState({
    total_transaction_volumes: 0,
    registered_users_count: "0",
    ad_listing_count: "0",
    total_completed_trades: "0",
  });

  const [refCode, setRefCode] = useState("");
  const [copied, setCopied] = useState(false);
  const [balance, setBalance] = useState({ asset: "USDT", free: "0.00" });
  const [isLoading, setIsLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState({
    date_range: {
      start_date: "",
      end_date: "",
    },
    overview: {
      total_trades: 0,
      completed_trades: 0,
      pending_trades: 0,
      cancelled_trades: 0,
      completion_rate: 0,
      total_volume: 0,
      average_trade_amount: 0,
    },
    top_currency_pairs: [],
    status_distribution: [],
  });
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const res = await customFetchWithToken.get("/dashboard/");
      setDashboardData({
        total_transaction_volumes: Number(
          res.data.total_transaction_volumes
        ).toFixed(2),
        registered_users_count: res.data.registered_users_count,
        ad_listing_count: res.data.ad_listing_count,
        total_completed_trades: res.data.total_completed_trades,
      });
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAnalyticsData = async () => {
    try {
      setAnalyticsLoading(true);
      const res = await customFetchWithToken.get("/analytics/trade-analytics/");

      // Extract data from the nested response structure
      if (res.data && res.data.data) {
        setAnalyticsData({
          date_range: res.data.data.date_range || {
            start_date: "",
            end_date: "",
          },
          overview: res.data.data.overview || {
            total_trades: 0,
            completed_trades: 0,
            pending_trades: 0,
            cancelled_trades: 0,
            completion_rate: 0,
            total_volume: 0,
            average_trade_amount: 0,
          },
          top_currency_pairs: res.data.data.top_currency_pairs || [],
          status_distribution: res.data.data.status_distribution || [],
        });
      }
    } catch (error) {
      console.log("Error fetching analytics data:", error);
      toast.error("Failed to load analytics data");
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const getWalletBalance = async () => {
    try {
      const res = await customFetchWithToken.get("/get-wallet-balance/");
      setBalance(res.data.data[0]);
    } catch (error) {
      console.log(error);
      toast.error(error.response.data.message);
    }
  };

  const generateRefCode = async () => {
    const res = await customFetchWithToken.post("/referral-code/");
    setRefCode(res.data.referral_link);
  };

  const handleCopy = () => {
    setCopied(true);
    toast.success("Referral link copied to clipboard!");
  };

  useEffect(() => {
    fetchDashboardData();
    generateRefCode();
    getWalletBalance();
    fetchAnalyticsData();
  }, []);

  const dashboardTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Dashboard</h1>
      <p className={styles.pageSubtitle}>
        Overview of your account activity and balances •{" "}
        {new Date().toLocaleDateString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        })}
      </p>
    </div>
  );

  return (
    <Layout title={dashboardTitle}>
      <div className={styles.dashboardContainer}>
        {/* Header Section - Hidden on desktop, shown only on mobile */}
        <div className={styles.dashboardHeader}>
          <div className={styles.headerContent}>
            <h1 className={styles.pageTitle}>Dashboard</h1>
            <p className={styles.pageSubtitle}>
              Overview of your account activity and balances •{" "}
              {new Date().toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>💰</div>
            <div className={styles.statLabel}>Total Transaction Volume</div>
            <div className={styles.statValue}>
              ${dashboardData.total_transaction_volumes}
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👥</div>
            <div className={styles.statLabel}>Total Completed Trades</div>
            <div className={styles.statValue}>
              {dashboardData.total_completed_trades}
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>📝</div>
            <div className={styles.statLabel}>Total Active Listings</div>
            <div className={styles.statValue}>
              {dashboardData.ad_listing_count}
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👤</div>
            <div className={styles.statLabel}>Total Users</div>
            <div className={styles.statValue}>
              {dashboardData.registered_users_count}
            </div>
          </div>
        </div>

        {/* Transaction Overview */}
        <div className={styles.overviewAndGraphContainer}>
          <div className={styles.transactionOverview}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Transaction Overview</h2>
              <span className={styles.periodText}>
                {analyticsData.date_range.start_date &&
                analyticsData.date_range.end_date
                  ? `${new Date(
                      analyticsData.date_range.start_date
                    ).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    })} - ${new Date(
                      analyticsData.date_range.end_date
                    ).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    })}`
                  : "Last 30 Days"}
              </span>
            </div>

            <div className={styles.statsContainer}>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>📊 Total Trades</div>
                <div className={styles.statItemValue}>
                  {analyticsLoading ? (
                    <div className={styles.loadingSpinner}></div>
                  ) : (
                    analyticsData.overview.total_trades
                  )}
                </div>
                <div className={styles.statItemSubtext}>
                  Total trades in period
                </div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>✅ Completed Trades</div>
                <div className={styles.statItemValue}>
                  {analyticsLoading ? (
                    <div className={styles.loadingSpinner}></div>
                  ) : (
                    analyticsData.overview.completed_trades
                  )}
                </div>
                <div className={styles.statItemSubtext}>
                  Success rate:{" "}
                  {analyticsLoading
                    ? "..."
                    : `${analyticsData.overview.completion_rate}%`}
                </div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>⏳ Pending Trades</div>
                <div className={styles.statItemValue}>
                  {analyticsLoading ? (
                    <div className={styles.loadingSpinner}></div>
                  ) : (
                    analyticsData.overview.pending_trades
                  )}
                </div>
                <div className={styles.statItemSubtext}>In pending state</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>❌ Cancelled Trades</div>
                <div className={styles.statItemValue}>
                  {analyticsLoading ? (
                    <div className={styles.loadingSpinner}></div>
                  ) : (
                    analyticsData.overview.cancelled_trades
                  )}
                </div>
                <div className={styles.statItemSubtext}>
                  Failed or cancelled
                </div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>💰 Total Volume</div>
                <div className={styles.statItemValue}>
                  {analyticsLoading ? (
                    <div className={styles.loadingSpinner}></div>
                  ) : (
                    `$${Number(
                      analyticsData.overview.total_volume
                    ).toLocaleString()}`
                  )}
                </div>
                <div className={styles.statItemSubtext}>Total trade volume</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>📈 Average Trade</div>
                <div className={styles.statItemValue}>
                  {analyticsLoading ? (
                    <div className={styles.loadingSpinner}></div>
                  ) : (
                    `$${Number(
                      analyticsData.overview.average_trade_amount
                    ).toLocaleString()}`
                  )}
                </div>
                <div className={styles.statItemSubtext}>Per transaction</div>
              </div>
            </div>
          </div>

          {/* Graph Section */}
          <div className={styles.graphSection}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Transaction Graph</h2>
            </div>
            <div className={styles.graphContainer}>
              <Graph />
            </div>
          </div>
        </div>

        {/* Wallet Balance */}
        <div className={styles.walletSection}>
          <div className={styles.walletHeader}>
            <h2 className={styles.walletTitle}>Wallet Balance</h2>
            <button
              className={styles.manageButton}
              onClick={() => router.push("/pages/remflowFunds")}
            >
              <span>Manage Wallet</span>
              <span>→</span>
            </button>
          </div>

          <div className={styles.walletCard}>
            <div className={styles.walletInfo}>
              <div className={styles.walletIcon}>
                <Image
                  src="/assets/payments/tether.svg"
                  alt="Tether USDT"
                  width={28}
                  height={28}
                />
              </div>
              <div className={styles.walletDetails}>
                <div className={styles.walletCurrency}>{balance.asset}</div>
              </div>
            </div>
            <div className={styles.walletBalance}>
              {isLoading ? (
                <div className={styles.loadingSpinner}></div>
              ) : (
                `${Number(balance.free).toFixed(2)} ${balance.asset}`
              )}
            </div>
          </div>
        </div>

        {/* Currency Pairs */}
        <div className={styles.currencyPairsSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Top Currency Pairs</h2>
          </div>

          <div className={styles.pairsGrid}>
            {analyticsLoading ? (
              <div className={styles.loadingContainer}>
                <div className={styles.loadingSpinner}></div>
                <span>Loading currency pairs...</span>
              </div>
            ) : analyticsData.top_currency_pairs.length > 0 ? (
              analyticsData.top_currency_pairs.map((pair, index) => (
                <div key={index} className={styles.pairCard}>
                  <div className={styles.pairHeader}>
                    <div className={styles.currencyIcon}>
                      {pair.payin_currency__currency_code?.substring(0, 3) ||
                        "N/A"}
                    </div>
                    <div className={styles.currencyName}>
                      {pair.payin_currency__currency_code}/
                      {pair.payout_currency__currency_code}
                    </div>
                  </div>
                  <div className={styles.pairStats}>
                    <span>{pair.trade_count} trades</span>
                    <span>${Number(pair.total_volume).toLocaleString()}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>📊</div>
                <h3 className={styles.emptyTitle}>No Currency Pairs</h3>
                <p className={styles.emptyDescription}>
                  No currency pair data available for this period.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Status Distribution */}
        <div className={styles.statusDistributionSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Trade Status Distribution</h2>
          </div>

          <div className={styles.statusGrid}>
            {analyticsLoading ? (
              <div className={styles.loadingContainer}>
                <div className={styles.loadingSpinner}></div>
                <span>Loading status distribution...</span>
              </div>
            ) : analyticsData.status_distribution.length > 0 ? (
              analyticsData.status_distribution.map((status, index) => {
                const getStatusIcon = (statusName) => {
                  switch (statusName.toLowerCase()) {
                    case "completed":
                      return "✅";
                    case "pending":
                      return "⏳";
                    case "cancelled":
                      return "❌";
                    case "rejected":
                      return "🚫";
                    case "ongoing":
                      return "🔄";
                    default:
                      return "📊";
                  }
                };

                const getStatusColor = (statusName) => {
                  switch (statusName.toLowerCase()) {
                    case "completed":
                      return "#10B981";
                    case "pending":
                      return "#F59E0B";
                    case "cancelled":
                      return "#EF4444";
                    case "rejected":
                      return "#DC2626";
                    case "ongoing":
                      return "#3B82F6";
                    default:
                      return "#64748B";
                  }
                };

                const percentage =
                  analyticsData.overview.total_trades > 0
                    ? (
                        (status.count / analyticsData.overview.total_trades) *
                        100
                      ).toFixed(1)
                    : 0;

                return (
                  <div key={index} className={styles.statusCard}>
                    <div className={styles.statusHeader}>
                      <div
                        className={styles.statusIcon}
                        style={{
                          backgroundColor: getStatusColor(status.order_status),
                        }}
                      >
                        {getStatusIcon(status.order_status)}
                      </div>
                      <div className={styles.statusName}>
                        {status.order_status.charAt(0).toUpperCase() +
                          status.order_status.slice(1)}
                      </div>
                    </div>
                    <div className={styles.statusStats}>
                      <div className={styles.statusCount}>{status.count}</div>
                      <div className={styles.statusPercentage}>
                        {percentage}%
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>📊</div>
                <h3 className={styles.emptyTitle}>No Status Data</h3>
                <p className={styles.emptyDescription}>
                  No status distribution data available for this period.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Referral Section */}
        <div className={styles.referralSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>
              Invite Friends & Earn Rewards
            </h2>
          </div>

          <div className={styles.referralContent}>
            <p>
              Share your unique referral link with friends and earn rewards when
              they sign up and complete their first transaction on Remflow.
            </p>

            <div className={styles.referralCode}>
              <span className={styles.codeText}>{refCode}</span>
              <CopyToClipboard text={refCode} onCopy={handleCopy}>
                <button className={styles.copyButton}>
                  Copy Referral Link
                </button>
              </CopyToClipboard>
            </div>

            <div className={styles.referralSteps}>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>1</div>
                <div className={styles.stepTitle}>Share Your Link</div>
                <div className={styles.stepDescription}>
                  Send your code to friends
                </div>
              </div>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>2</div>
                <div className={styles.stepTitle}>Friends Sign Up</div>
                <div className={styles.stepDescription}>
                  They create an account with your code
                </div>
              </div>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>3</div>
                <div className={styles.stepTitle}>Earn Rewards</div>
                <div className={styles.stepDescription}>
                  Get bonuses for each referral
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
