@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}





.header {
  color: #000;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;

}

.searchBarWrapper {
  display: flex;
  width: 100%;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    align-items: stretch;
  }
}

.tradeButtons {
  display: flex;
  width: 140px;

  height: 40px;
}



.currencyInputContainer {
  display: flex;
  width: 350px;
  height: 40px;
  padding: 10px;
  align-items: center;
  gap: 15px;
  border-radius: 5px;
  background: #EBEBEB;

}

.headerCurrency {
  color: #000;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.currencyFrom {
  width: 97px;
  display: flex;
  height: 28px;
  border-radius: 5px;
  border: 1px solid #EBEBEB;
  background: #FFF;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fromText {
  display: flex;
  width: 32px;
  padding: 10px 2px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 5px 0px 0px 5px;
  border-right: 1px solid #EBEBEB;
  color: #858585;
  text-align: center;
  font-family: Poppins;
  font-size: 8px;
  font-style: normal;
  font-weight: 300;

}



.fromInput {
  width: 100%;
  background-color: transparent;
  border-radius: 0px 5px 5px 0px;

}

.fromInput input {
  width: 90%;
  outline: none;
  border: none;
  background-color: transparent;
  color: #858585;

}

.enterAmountContainer {
  display: flex;
  width: 100%;
  height: 40px;
  align-items: center;
  gap: 10px;
  border-radius: 5px;
  background: #fff;

  ;
}

.enterAmountContainer input {
  width: 90%;
  outline: none;
  border: none;
  background-color: transparent;
  color: #858585;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}

.paymentSelect {
  width: 300px;
  padding: 10px;
  padding-right: 83px;
  outline: none;
  border: none;
  background-color: #50CD89;

}





.filterCont {
  display: flex;
  justify-content: center;
  align-items: center;
}

.postionlWrapper {
  position: relative;
}

.filterCont12 {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: flex-end;
  margin-top: 0;

  @media screen and (max-width: 576px) {
    position: relative;
    top: auto;
    right: auto;
    display: flex;
    justify-content: center;
    margin-top: 12px;
    width: 100%;
  }
}

.filterCont12 .filterCont12 {
  display: contents;
}

.filterAdvance,
.highlightFilter,
.filter {
  display: flex;
  width: 44px;
  height: 44px;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;

  @media screen and (max-width: 576px) {
    width: 48px;
    height: 48px;
    border-radius: 12px;
  }
}

.filterAdvance {
  border: 2px solid #e2e8f0;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filterAdvance::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.filterAdvance:hover {
  border-color: #f59e0b;
  background: #fef3c7;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(245, 158, 11, 0.2);
}

.filterAdvance:hover::before {
  left: 100%;
}

.filterAdvance:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.highlightFilter {
  border: 2px solid #f59e0b;
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  box-shadow: 0 3px 8px rgba(245, 158, 11, 0.3);
}

.highlightFilter:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.highlightFilter1 path {
  fill: #ffffff;
}

.filter {
  border: none;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.3);
}

.filter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.filter:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.filter:hover::before {
  left: 100%;
}

.filter:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.4);
}

/* .filter {
  display: flex;
  width: 35.1px;
  height: 35px;
  padding: 8px 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 5px;
  border: 1px solid #000;
  background: #FFF;
  margin: 0 5px;
} */

/* results */


.resultsSectionCont {
  display: flex;
  width: 100%;
  height: auto;
  padding: 24px;
  flex-direction: column;
  box-sizing: border-box;

  @media screen and (max-width : 576px) {
    display: flex;
    padding: 0px;
    margin-top: 0;
  }

}

.resultsSection {
  display: flex;
  flex-direction: column;
  padding: 30px 0px;
  gap: 20px;
  border-radius: 20px;
  background: #fff;
  display: flex;
  width: 100%;
  /* justify-content: space-around; */
  /* align-items: center; */
}

/* tables */

.tableHeads {
  width: 100%;
  height: 45px;
  flex-shrink: 0;
  border-radius: 5px;
  background: #fafafa4f;
}



.tableHeads1 {
  width: 100%;
  height: 77px;
  flex-shrink: 0;
  border-radius: 5px;
  background: #FAFAFA;
  color: #000;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  justify-content: space-between;
  display: flex;
  align-items: center;

  /* 78.571% */
}

.tableAdvert {
  width: 120px;
}

.tableBody {

  text-align: center;
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;

  /* 91.667% */
}

.tableRow {
  padding: 20px 0;
}

.advBtn {
  /* display: flex;
  padding: 8px 2px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 5px;
  background: #F8F5FF;
  text-align: center;
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  align-self: center;
  display: flex;
  margin-top: 15px; */

  text-align: center;
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
}


.buyUsdtBtn {
  /* padding: 8px 2px;
  height: 2px;
  gap: 10px;
  border-radius: 5px;
  background: #F8F5FF;
  text-align: center;
  color: #50CD89;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  border-radius: 5px;
  background: #E8FFF3; */

  text-align: center;
  color: #50CD89;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;

}

.ProfileDiv {
  /* width: 20%;
  display: flex; */
}

.ProfilePic {
  width: 20px;
  height: 30px;
  flex-shrink: 0;
  border-radius: 50%;
  background-color: gray;
}

/* tables */

/* modal */

.modalCont {
  display: flex;
  width: 700px;
  padding-bottom: 0px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 30px;
}

.modalTop {
  width: 100%;
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid #858585;
}

.left {
  width: 85%;
  height: 63px;
}


.right {
  width: 15%;
  /* background-color: #50CD89; */
  display: flex;
  justify-content: flex-end;
  /* align-items: center; */
}

.modalProfileWrapper {
  display: flex;
  width: 100%;
  justify-content: flex-start;
  align-items: center;


}

.orders {

  color: #000;
  font-family: Poppins;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  margin-left: 10px;
  margin-right: 10px;

}


.modalName {
  color: #000;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 11px;
  /* 78.571% */
}

.modalBottom {
  width: 100%;
  display: flex;

}

.leftB {
  width: 50%;
  display: flex;
  margin-right: auto;
  flex-direction: column;

}

.tHeader {
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 11px;

  text-decoration-line: underline;
}

.termsPara {
  color: #363636;
  font-family: Poppins;
  font-size: 11px;
  font-style: normal;
  font-weight: 300;

}

.info {
  border: 1px solid #EBEBEB;
  background: #FFF;
  display: inline-flex;
  padding: 10px;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}


.infoPoints {
  display: flex;
  color: #000;
  font-family: Poppins;
  font-size: 10px;
  font-style: normal;
  font-weight: 300;
  color: #000;
  font-family: Poppins;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;

}

.highlight {

  font-weight: 500;
}

.terms {
  margin-top: 30px;
}

.tHeader {
  margin-bottom: 20px;
}

.rightB {
  width: 40%;
}

.payInput {
  display: flex;
  height: 40px;
  padding: 10px;
  justify-content: space-between;
  align-items: center;
  border-radius: 5px;
  background: #F9F9F9;
}

.paySelect {
  width: 70px;
  color: #000;
  font-family: Poppins;
  font-size: 10px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  height: 100%;
  background-color: transparent;
  border: 1px solid #BABABA;
}

.payInput input {
  outline: none;
  border: none;
  height: 100%;
  color: #BABABA;
  font-family: Poppins;
  font-size: 10px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  background-color: transparent;
}

.buttonBBUY {
  margin-top: 20px;
  display: flex;
  width: 93%;
  height: 40px;
  padding: 8px 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 5px;
  background: #E8FFF3;
  color: #50CD89;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 11px;
}


/* modal */

/* results */



/* extra*/

.firstNameInput {
  position: relative;
  display: flex;
  align-items: center;
}

.firstNameInput input,
.firstNameInput select {
  width: 100%;
  /* height: 44px; */
  padding: 10px 14px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: #ffffff;
  color: #1f2937;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  @media screen and (max-width: 576px) {
    height: 44px;
    padding: 10px 14px;
    font-size: 14px;
  }
}

.firstNameInput input:focus,
.firstNameInput select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.firstNameInput input:hover,
.firstNameInput select:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.firstNameInput input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.firstNameInput select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 36px;

  @media screen and (max-width: 576px) {
    padding-right: 36px;
  }
}

.firstNameInput select option {
  padding: 8px 12px;
  font-size: 14px;
  color: #1f2937;
  background: #ffffff;
}

.noData {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px 24px;
  color: #6b7280;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 16px;
  font-weight: 500;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  margin: 24px 0;
}


/* new */
.firstformWrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    align-items: stretch;
  }
}

.firstformWrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4f46e5 0%, #06b6d4 50%, #10b981 100%);
  border-radius: 16px 16px 0 0;
}

.filterTabs {
  display: flex;
  gap: 20px;
  width: 100%;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    gap: 16px;
  }
}

.hideTabs {
  display: none;
}

.firstName {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  flex: 1;

  @media screen and (max-width: 576px) {
    width: 100%;
    min-width: unset;
  }
}

.firstNameMob {


  @media screen and (max-width: 576px) {
    width: 90%;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    margin: 10px 0px;
    margin-right: 20px;
    height: auto;
  }
}

.mobViewFirstRowLeftWrapper {


  @media screen and (max-width: 576px) {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }
}

.mobViewFirstRowCont {


  @media screen and (max-width: 576px) {
    width: 80%;
    display: flex;
    justify-content: flex-start;
  }
}

.filterMobCont {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

}


.firstName1 {
  width: 45%;
  height: 70px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 18px;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin: 20px 0;
  }
}

.firstName1:active {
  scale: 0.9;
}

.firstNameLabel {
  color: #374151;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: -0.01em;
  margin-bottom: 4px;

  @media screen and (max-width: 576px) {
    font-size: 13px;
  }
}

.mobLabel {
  @media screen and (max-width: 576px) {

    font-size: 14px !important;
  }
}

.sec_firstName {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  flex: 1;

  @media screen and (max-width: 576px) {
    width: 100%;
    min-width: unset;
  }
}

.sec_firstNameMob {

  @media screen and (max-width: 576px) {
    width: 100%;

    height: 40px;


  }
}

.mobViewTopSearchBarCont {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  width: 100%;
  position: sticky;
  z-index: 9;
  top: 45px;
  background-color: #ffffff;

  @media screen and (max-width: 576px) {
    z-index: 0;
  }
}

.mobViewTopSearchBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
}

.searchOptions {
  font-size: 12px;
  width: 20px;
}

.searchOptionsButton {
  font-size: 12px;
  padding: 2px 5px;
  background-color: #4153ED;
  color: white;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 5px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;

  @media screen and (max-width: 576px) {
    display: none;
  }

}

.searchOptionsButtonMob {
  font-size: 12px;
  padding: 2px 5px;
  background-color: #4153ED;
  color: white;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 5px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mobSelect {
  width: 50px;
}



.mobView {
  display: none;

  @media screen and (max-width: 576px) {
    width: 100%;
    display: flex !important;
    flex-direction: column;
    align-items: center;
    position: relative;
    background: transparent;
    padding: 0 1%;
    margin-top: 0;
    z-index: 1;
    box-sizing: border-box;
    gap: 0;
  }
}

.desktopView {
  display: block;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.mobView1 {
  display: none;

  @media screen and (max-width: 576px) {
    display: block;
    width: 100%;
    top: 150px;
    background-color: #fdfbfb;
  }
}

.paginationCont {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16px 0;
  padding: 12px;
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.paginationContMob {
  display: none;

  @media screen and (max-width: 576px) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 98%;
    margin: 16px auto 20px auto;
    padding: 16px 20px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    transition: all 0.3s ease;
  }

  @media screen and (max-width: 480px) {
    width: 96%;
    padding: 14px 16px;
  }

  @media screen and (max-width: 360px) {
    width: 95%;
    padding: 12px 14px;
  }
}

.pagiCont {
  display: flex !important;

  @media screen and (max-width: 576px) {
    padding: inherit;
  }
}

.mobViewSearchBtn {
  display: none;
}

@media screen and (max-width: 576px) {
  .mobViewSearchBtn {
    width: 100% !important;
    height: 40px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    background-color: #4153ED !important;
    border-radius: 5px !important;
    border: none !important;
    margin-top: 10px !important;
    cursor: pointer !important;
  }
}

.mobSearchBtn {
  display: none;

  @media screen and (max-width: 576px) {
    display: block;
    color: #f9f9f9;
    background-color: none;
    font-size: 18px;
    font-weight: 400;
    font-family: poppins;
    cursor: pointer;
  }

}

.pagination {
  margin: 0px 4px;
  font-size: 15px;
  text-decoration: none;
  list-style: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.paginationHead {
  font-size: 15px;
  text-decoration: none;
  list-style: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.paginationAtag {
  color: #4153ED;
  font-size: 18px;
  text-decoration: none;
}

.active {

  background-color: #f1f0f0;
  padding: 10px;
  border-radius: 5px;

}


.paginationCont button {
  margin: 0px 10px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.filterArrowUp {
  transform: rotate(180deg);
  position: absolute;
  bottom: -10px;
  left: 1.8px;
  cursor: pointer;
}

.filterArrowDown {
  position: absolute;
  top: -7px;
  bottom: 0px;
  cursor: pointer;
}

.filterArrowUp:hover {
  fill: #c9b8b8;
}

.filterArrowDown:hover {
  fill: #c9b8b8;
}

.filterArrowUp:active {

  fill: gray;
}

.filterArrowDown:active {

  fill: gray;
}

.loader {
  display: flex;
  justify-content: center;
  align-items: center;
}

.rateSortContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.rateSortContainerMob {
  display: none;

  @media screen and (max-width: 576px) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 98%;
    margin: 0 auto 16px auto;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 16px 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    transition: all 0.3s ease;
  }

  @media screen and (max-width: 480px) {
    width: 96%;
    padding: 14px 16px;
  }

  @media screen and (max-width: 360px) {
    width: 95%;
    padding: 12px 14px;
  }
}

.upRate,
.downRate {
  fill: #64748b;
  cursor: pointer;
  padding: 10px;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upRate:hover,
.downRate:hover {
  fill: #4f46e5;
  background: rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.upRate:active,
.downRate:active {
  transform: translateY(0);
  background: rgba(79, 70, 229, 0.2);
}

.headFilter {
  color: #1e293b;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.01em;
  flex: 1;
}

.currencyTabs {
  display: flex;
  gap: 4px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 4px;
  border-radius: 16px;
  margin-bottom: 0;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  flex-shrink: 1; /* Allow shrinking */
  min-width: 0; /* Allow shrinking */
  max-width: 100%; /* Fit within container */

  @media screen and (max-width: 576px) {
    margin-bottom: 16px;
  }
}

.currencyTabs::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #4f46e5 0%, #06b6d4 50%, #10b981 100%);
  border-radius: 16px 16px 0 0;
}

.currencyToUsdtActive {
  padding: 12px 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(220, 38, 38, 0.4);
  letter-spacing: -0.02em;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  text-transform: uppercase;
  min-width: 110px;
  text-align: center;
  white-space: nowrap;
  flex-shrink: 1;
}

.currencyToUsdtActive::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.currencyToUsdtActive:hover::before {
  left: 100%;
}

.currencyToUsdt {
  padding: 12px 16px;
  border-radius: 12px;
  background: #ffffff;
  color: #dc2626;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.02em;
  border: 2px solid #fecaca;
  text-transform: uppercase;
  min-width: 110px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  white-space: nowrap;
  flex-shrink: 1;
}

.currencyToUsdt:hover {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  color: #dc2626;
  border-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

.UsdtToCurrencyActive {
  padding: 12px 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, #eab308 0%, #f59e0b 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(234, 179, 8, 0.4);
  letter-spacing: -0.02em;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  text-transform: uppercase;
  min-width: 110px;
  text-align: center;
  white-space: nowrap;
  flex-shrink: 1;
}

.UsdtToCurrencyActive::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.UsdtToCurrencyActive:hover::before {
  left: 100%;
}

.UsdtToCurrency {
  padding: 12px 16px;
  border-radius: 12px;
  background: #ffffff;
  color: #eab308;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.02em;
  border: 2px solid #fef3c7;
  text-transform: uppercase;
  min-width: 110px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  white-space: nowrap;
  flex-shrink: 1;
}

.UsdtToCurrency:hover {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  color: #eab308;
  border-color: #eab308;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.15);
}

.UsdtToCurrency:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #4f46e5;
  border-color: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

/* Best Rate Bento Box - Full width container below currency tabs */
.bestRateBentoBox {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
  border: 2px solid #10b981;
  border-radius: 20px;
  padding: 24px 32px;
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.15);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bestRateBentoBox:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 40px rgba(16, 185, 129, 0.2);
}

.bestRateBentoBox::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981 0%, #059669 25%, #047857 50%, #059669 75%, #10b981 100%);
  border-radius: 20px 20px 0 0;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.bestRateContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  z-index: 2;
}

.bestRateHeader {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.bestRateIcon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.bestRateTitle {
  color: #047857;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  flex: 1;
  min-width: 200px;
}

.bestRateValue {
  color: #047857;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 28px;
  font-weight: 900;
  letter-spacing: -0.03em;
  text-shadow: 0 2px 4px rgba(4, 120, 87, 0.1);
  background: linear-gradient(135deg, #047857 0%, #059669 50%, #10b981 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 8px 16px;
  border: 2px solid #059669;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.bestRateDescription {
  color: #065f46;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  opacity: 0.9;
  width: 100%;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    font-size: 14px;
  }
}

.loadMore {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-family: poppins;
  margin: 10px 0px;
}

/* Button container fix */
.sec_firstName:last-child {
  flex: 0 0 auto;
  min-width: auto;
  align-self: flex-end;
}

/* Main controls container - holds both top row and bottom bento box */
.mainControlsContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
  padding: 0 4px;
  animation: slideInFromTop 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  @media screen and (max-width: 576px) {
    display: none;
  }
}

/* Top row - Currency tabs and sort controls on same line */
.topControlsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  min-height: 60px; /* Ensure consistent height */
  width: 100%;
  overflow: hidden; /* Prevent overflow */
}

/* Currency tabs container - maintains space even when empty */
.currencyTabsContainer {
  flex: 0 1 auto; /* Don't grow, can shrink, auto basis */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  min-width: 0; /* Allow shrinking */
  max-width: 50%; /* Limit to half the container */
}

/* Modern table controls positioned at top right */
.tableControlsContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex: 0 1 auto; /* Don't grow, can shrink, auto basis */
  min-width: 0; /* Allow shrinking */
  max-width: 60%; /* Ensure it doesn't take too much space */

  @media screen and (max-width: 576px) {
    display: none;
  }
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.tableControls {
  display: flex;
  align-items: center;
  gap: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tableControls:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.tableControls::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
  border-radius: 1px;
}

/* Modern rate filter controls */
.rateFilterCompact {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filterLabel {
  color: #374151;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: -0.01em;
  white-space: nowrap;
}

.rateFilterSelect {
  min-width: 140px;
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  color: #1f2937;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
}

.rateFilterSelect:hover {
  border-color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.rateFilterSelect:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.rateFilterSelect option {
  padding: 8px 12px;
  font-size: 13px;
  color: #1f2937;
  background: #ffffff;
}

.sortButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

.sortButton:hover {
  border-color: #4f46e5;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
}

.sortButton:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.2);
}

.sortButton svg {
  fill: #6b7280;
  transition: fill 0.2s ease;
}

.sortButton:hover svg {
  fill: #4f46e5;
}

/* Modern compact pagination styles */
.paginationCompact {
  display: flex;
  align-items: center;
  transition: opacity 0.3s ease;
}

.paginationLoading {
  opacity: 0.6;
  pointer-events: none;
}

.paginationSection {
  display: flex;
  align-items: center;
  gap: 20px;
}

.pageInfo {
  display: flex;
  align-items: center;
}

.pageInfoText {
  color: #6b7280;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: -0.01em;
  white-space: nowrap;
}

.paginationContainer {
  display: flex !important;
  align-items: center;
  gap: 6px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.paginationPage,
.paginationPrev,
.paginationNext,
.paginationBreak {
  list-style: none;
}

.paginationLink,
.paginationPrevLink,
.paginationNextLink,
.paginationBreakLink {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  color: #6b7280;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.01em;
}

.paginationLink:hover,
.paginationPrevLink:hover,
.paginationNextLink:hover {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.15);
}

.paginationActive .paginationLink {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: #ffffff;
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

.paginationBreakLink {
  border: none;
  background: transparent;
  cursor: default;
  color: #9ca3af;
}

.paginationBreakLink:hover {
  background: transparent;
  transform: none;
  box-shadow: none;
  color: #9ca3af;
}

/* Previous/Next button specific styles */
.paginationPrevLink,
.paginationNextLink {
  font-size: 16px;
  font-weight: 700;
  min-width: 36px;
  height: 32px;
}

/* Hide original pagination and rate sort containers on desktop */
.paginationCont {
  display: none;
}

.rateSortContainer {
  display: none;
}

/* Search Ads Header Section */
.searchAdsHeader {
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  display: none;
}

@media (max-width: 576px) {
  .searchAdsHeader {
    display: block;
    margin-bottom: 20px;
    padding: 0 16px;
    text-align: center;
  }
}

.headerContent {
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
}

.pageTitle {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

@media (max-width: 576px) {
  .pageTitle {
    font-size: 24px;
  }
}

.pageSubtitle {
  font-size: 14px;
  color: #64748B;
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
}

@media (max-width: 576px) {
  .pageSubtitle {
    font-size: 13px;
  }
}